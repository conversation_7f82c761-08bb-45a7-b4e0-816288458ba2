// Pin definitions for the DRV8833 motor driver
const int leftMotorIN1 = 26;
const int leftMotorIN2 = 25;
const int rightMotorIN3 = 33;
const int rightMotorIN4 = 32;

// Ultrasonic sensor pins
const int frontTrigPin = 23;
const int frontEchoPin = 22;
const int leftTrigPin = 19;
const int leftEchoPin = 18;
const int rightTrigPin = 5;
const int rightEchoPin = 4;

// Thresholds
const int front_wall = 10;
const int OBSTACLE_THRESHOLD = 20;  // obstacle detection in cm
const int DESIRED_LEFT = 9;        // desired distance from left wall
const int LEFT_TOLERANCE = 5;       // acceptable range (+/-)

void setup() {
  Serial.begin(9600);
  Serial.println("Left-Hand Maze Solver with Stops at Decision Points");

  pinMode(leftMotorIN1, OUTPUT);
  pinMode(leftMotorIN2, OUTPUT);
  pinMode(rightMotorIN3, OUTPUT);
  pinMode(rightMotorIN4, OUTPUT);

  pinMode(frontTrigPin, OUTPUT);
  pinMode(frontEchoPin, INPUT);
  pinMode(leftTrigPin, OUTPUT);
  pinMode(leftEchoPin, INPUT);
  pinMode(rightTrigPin, OUTPUT);
  pinMode(rightEchoPin, INPUT);

  stopMotors();
  delay(1000);
}

// ---------------- Distance Measurement ----------------
long getDistance(int trigPin, int echoPin) {
  digitalWrite(trigPin, LOW);
  delayMicroseconds(2);

  digitalWrite(trigPin, HIGH);
  delayMicroseconds(10);
  digitalWrite(trigPin, LOW);

  long duration = pulseIn(echoPin, HIGH, 20000);
  if (duration == 0) return 999; 
  return duration * 0.0343 / 2;
}

// ---------------- Motor Control ----------------
void moveForward() {
  digitalWrite(leftMotorIN1, HIGH);
  digitalWrite(leftMotorIN2, LOW);
  digitalWrite(rightMotorIN3, HIGH);
  digitalWrite(rightMotorIN4, LOW);
}

void adjustLeft() {  // small correction left
  digitalWrite(leftMotorIN1, LOW);
  digitalWrite(leftMotorIN2, LOW);
  digitalWrite(rightMotorIN3, HIGH);
  digitalWrite(rightMotorIN4, LOW);
  delay(50);
  stopMotors();
}

void adjustRight() { // small correction right
  digitalWrite(leftMotorIN1, HIGH);
  digitalWrite(leftMotorIN2, LOW);
  digitalWrite(rightMotorIN3, LOW);
  digitalWrite(rightMotorIN4, LOW);
  delay(50);
  stopMotors();
}

void turnLeft() {
  digitalWrite(leftMotorIN1, LOW);
  digitalWrite(leftMotorIN2, HIGH);
  digitalWrite(rightMotorIN3, HIGH);
  digitalWrite(rightMotorIN4, LOW);
  delay(150); // adjust for 90°
  stopMotors();
}

void turnRight() {
  digitalWrite(leftMotorIN1, HIGH);
  digitalWrite(leftMotorIN2, LOW);
  digitalWrite(rightMotorIN3, LOW);
  digitalWrite(rightMotorIN4, HIGH);
  delay(150);
  stopMotors();
}

void turnAround() {
  digitalWrite(leftMotorIN1, HIGH);
  digitalWrite(leftMotorIN2, LOW);
  digitalWrite(rightMotorIN3, LOW);
  digitalWrite(rightMotorIN4, HIGH);
  delay(500); // adjust for 180°
  stopMotors();
}

void stopMotors() {
  digitalWrite(leftMotorIN1, LOW);
  digitalWrite(leftMotorIN2, LOW);
  digitalWrite(rightMotorIN3, LOW);
  digitalWrite(rightMotorIN4, LOW);
}

// ---------------- Main Loop ----------------
void loop() {
  long frontDistance = getDistance(frontTrigPin, frontEchoPin);
  long leftDistance = getDistance(leftTrigPin, leftEchoPin);
  long rightDistance = getDistance(rightTrigPin, rightEchoPin);

  Serial.print("Front: "); Serial.print(frontDistance);
  Serial.print(" cm | Left: "); Serial.print(leftDistance);
  Serial.print(" cm | Right: "); Serial.println(rightDistance);

  // === At a decision point: STOP, then decide ===
  stopMotors();
  delay(200);

  if (leftDistance > OBSTACLE_THRESHOLD) {
    Serial.println("Decision: Turning LEFT");
    turnLeft();
    moveForward();
  } 
  else if (frontDistance > OBSTACLE_THRESHOLD) {
    Serial.println("Decision: FORWARD with wall-centering");

    // Centering while going straight
    if (leftDistance < DESIRED_LEFT - LEFT_TOLERANCE) {
      Serial.println("Too close → Adjusting RIGHT");
      adjustRight();
      moveForward();
    }
    else if (leftDistance > DESIRED_LEFT + LEFT_TOLERANCE) {
      Serial.println("Too far → Adjusting LEFT");
      adjustLeft();
      moveForward();
    }
    else {
      moveForward();
    }
  } 
  else if (rightDistance > OBSTACLE_THRESHOLD) {
    Serial.println("Decision: Turning RIGHT");
    turnRight();
    moveForward();
  } 
  else {
    Serial.println("Decision: Dead end → Turning AROUND");
    turnAround();
  }

  delay(80);
}

